/* MultiplePropsTable CSS Module */

.container {
  width: 100%;
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #1a1a1a;
  background-color: #ffffff;
  overflow: hidden;
  border: none;
}

.tabsHeader {
  display: flex;
  border-bottom: 1px solid #26557326;
  margin: 0;
  padding: 0;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  position: relative;
  flex-wrap: nowrap;
}

.tabsHeader::-webkit-scrollbar {
  display: none;
}

/* Subtle scroll indicators for better UX */
.tabsHeader::before,
.tabsHeader::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 20px;
  pointer-events: none;
  z-index: 1;
}

.tabsHeader::before {
  left: 0;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.9), transparent);
}

.tabsHeader::after {
  right: 0;
  background: linear-gradient(to left, rgba(255, 255, 255, 0.9), transparent);
}

.tab {
  padding: 10px 15px;
  border: none;
  background-color: transparent;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #73828c;
  border-bottom: 3px solid transparent;
  transition: all 0.15s ease-in-out;
  position: relative;
  outline: none;
  font-family: inherit;
  line-height: 1.4;
  flex-shrink: 0;
  white-space: nowrap;
  min-width: fit-content;
}

.tab:hover {
  color: #029cfd;
}

.tabActive {
  color: #029cfd;
  border-bottom-color: #029cfd;
  background-color: #ffffff;
  font-weight: 600;
}

.tabActive:hover {
  background-color: #ffffff;
  color: #029cfd;
}

.tabpanel {
  overflow: auto;
}

.table {
  font-family: "Nunito Sans", -apple-system, ".SFNSText-Regular", "San Francisco", BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif;
  width: 100%;
  border-collapse: separate;
  font-size: 13px;
  border-spacing: 0px;
  color: rgb(46, 52, 56);
  line-height: 20px;
  text-align: left;

}

.tableHeader {
  display: table-header-group;
  vertical-align: middle;
  unicode-bidi: isolate;
}

.th {
  padding: 10px 20px;
  padding-top: 25px;
  text-align: left;
  font-weight: 600;
  color: #2e3438bf;
  font-size: 13px;
  letter-spacing: 0.025em;
  border-bottom: 1px solid #26557326;
  line-height: 1.4;
}

.tableBody {
  display: table-row-group;
  vertical-align: middle;
  unicode-bidi: isolate;
  border-color: inherit;
  filter: drop-shadow(rgba(0, 0, 0, 0.1) 0px 1px 3px)
}

.tdFirst {
  border-left: 1px solid #26557326;
  font-weight: 700;

  :first-of-type {
    border-top-left-radius: 8px;
  }
}

.td {
  padding: 16px 20px;
  border-bottom: 1px solid #26557326;
  vertical-align: top;
  line-height: 1.6;
}

.tdLast {
  border-right: 1px solid #26557326;

  :first-of-type {
    border-top-right-radius: 8px;
  }
}

.nameCell {
  font-weight: bold;
  color: #2e3438;
  font-size: 13px;
  letter-spacing: -0.01em;
  padding: 10px 0px 10px 20px;
}

.descriptionCell {
  color: #2e3438;
  line-height: 1.6;
  padding: 10px 15px;
}

.defaultCell {
  color: #2e3438;
  font-size: 13px;
  letter-spacing: -0.01em;
  padding: 10px 20px 10px 15px;
}

.typeCode {
  padding: 2px 5px;
  border-radius: 3px;
  border: 1px solid rgb(236, 244, 249);
  color: rgba(46, 52, 56, 0.9);
  background-color: rgb(247, 250, 252);
  flex: 0 0 auto;
  font-family: ui-monospace, Menlo, Monaco, "Roboto Mono", "Oxygen Mono", "Ubuntu Monospace", "Source Code Pro", "Droid Sans Mono", "Courier New", monospace;
  font-size: 12px;
  word-break: break-word;
  white-space: normal;
  max-width: 100%;
  margin: 0px 4px 4px 0px;
  line-height: 13px;
}

.emptyState {
  padding: 24px 20px;
  color: #73828c;
  font-style: italic;
  text-align: center;
  background-color: #fafbfc;
}

.errorState {
  padding: 24px 20px;
  color: #d1242f;
  font-style: italic;
  text-align: center;
  background-color: #fff8f8;
  border: 1px solid #f8d7da;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    border-radius: 4px;
  }

  .tabsHeader {
    /* Add subtle scroll indicators on mobile */
    background: linear-gradient(90deg, transparent 0%, transparent 10px, #ffffff 10px, #ffffff calc(100% - 10px), transparent calc(100% - 10px), transparent 100%);
  }

  .tab {
    padding: 10px 16px;
    font-size: 13px;
    min-width: 120px; /* Ensure minimum touch target size */
  }

  .th,
  .td {
    padding: 12px 16px;
  }

  .descriptionCell {
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .tab {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 100px; /* Smaller minimum width for very small screens */
  }

  .th,
  .td {
    padding: 10px 12px;
  }

  .descriptionCell {
    max-width: 200px;
  }
}

/* Column width utilities */
.colName {
  width: 20%;
}

.colDescription {
  width: 60%;
}

.colDefault {
  width: 20%;
}